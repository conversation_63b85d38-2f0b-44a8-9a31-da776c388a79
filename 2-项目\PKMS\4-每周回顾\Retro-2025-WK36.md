# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >const currentProject = dv.current().file.path.split("/")[1];
> >await dv.view("0-辅助/Views/blockPriority", { dv, moment, folder: `3-过程资产/${currentProject}/阻碍`, sprintLength: 1 });
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >await dv.view("0-辅助/Views/techDebtReport", { dv });
> > ```

# 2. 流程改善

| 问题溯源                    | 根因分析                   | 改善行动                                                                   | 验收标准                                                            | 改善结果 |
| ----------------------- | ---------------------- | ---------------------------------------------------------------------- | --------------------------------------------------------------- | ---- |
| [[blocker-20250808-01]] | 开发超过当前认知的需求，且没有相应的知识积累 | ① 采用临时方案：执行augment code + Roo code作为新的开发策略<br>② 实时监控：启动4周的监控期，记录需求开发效果 | ① 短期验收：在4周监控期内，未出现完全无法解决的开发需求<br>② 长期验收：成功将临时方案转化为个人经验方法，并关闭此阻碍 |      |

# 3. 改善回顾

```dataviewjs
await dv.view("0-辅助/Views/improveSummary", { dv });
```